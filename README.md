# Offline-First CRUD Application

A React Native application built with TypeScript that demonstrates offline-first capabilities using RxDB with SQLite storage and CouchDB replication. This project showcases professional-level code quality.

## 🚀 Features

### Core Functionality

- **Offline-First Architecture**: All CRUD operations work without internet connection
- **Real-time Sync**: Automatic synchronization with CouchDB when online
- **Business Management**: Create, read, update, and delete businesses
- **Article Management**: Manage articles linked to businesses with quantity and pricing
- **Reactive Data**: Real-time UI updates using RxDB observables

### Technical Features

- **TypeScript**: Full type safety throughout the application
- **RxDB + SQLite**: Local database with reactive queries
- **CouchDB Replication**: Cloud synchronization with conflict resolution
- **React Navigation**: Professional navigation with stack and tab navigators
- **Network Monitoring**: Real-time network status detection
- **Error Handling**: Comprehensive error handling with user-friendly messages
- **Loading States**: Smooth loading indicators for all operations
- **Empty States**: Helpful empty states with actionable guidance

### UI/UX Features

- **Modern Design**: Clean, light theme with professional styling
- **Responsive Layout**: Optimized for various screen sizes
- **Smooth Animations**: Native animations and transitions
- **Touch Feedback**: Proper touch feedback and active states
- **Status Indicators**: Online/offline and sync status indicators
- **Form Validation**: Real-time form validation with error messages

## 📋 Requirements

- Node.js 18+
- React Native CLI
- Android Studio (for Android development)
- Xcode (for iOS development, macOS only)

## 🛠 Installation

### 1. Clone the Repository

```bash
git clone <repository-url>
cd OfflineFirstCRUD
```

### 2. Install Dependencies

```bash
npm install
```

### 3. Install iOS Dependencies (iOS only)

```bash
cd ios && pod install && cd ..
```

### 4. Configure CouchDB (Optional)

Edit `src/utils/constants.ts` to configure your CouchDB instance:

```typescript
export const COUCHDB_URL = 'https://your-couchdb-instance.com';
export const COUCHDB_USERNAME = 'your-username';
export const COUCHDB_PASSWORD = 'your-password';
```

**Note**: The app works fully offline without CouchDB configuration. CouchDB is only needed for cloud synchronization.

## 🚀 Running the Application

### Start Metro Bundler

```bash
npm start
```

### Run on Android

```bash
npm run android
```

### Run on iOS

```bash
npm run ios
```

## 🏗 Project Structure

```
src/
├── components/           # Reusable UI components
│   ├── common/          # Generic components (Button, Input, etc.)
│   ├── business/        # Business-specific components
│   └── article/         # Article-specific components
├── screens/             # Screen components
│   ├── BusinessListScreen.tsx
│   ├── BusinessFormScreen.tsx
│   ├── ArticleListScreen.tsx
│   └── ArticleFormScreen.tsx
├── database/            # Database configuration and models
│   ├── index.ts         # Database initialization
│   ├── schemas/         # RxDB schemas
│   └── collections/     # Collection definitions
├── services/            # Business logic and API services
│   ├── DatabaseService.ts
│   ├── SyncService.ts
│   └── NetworkService.ts
├── hooks/               # Custom React hooks
│   ├── useDatabase.ts
│   ├── useNetworkStatus.ts
│   └── useSyncStatus.ts
├── types/               # TypeScript type definitions
│   ├── Business.ts
│   ├── Article.ts
│   └── Database.ts
├── utils/               # Utility functions
│   ├── constants.ts
│   ├── helpers.ts
│   └── validation.ts
└── navigation/          # Navigation configuration
    └── AppNavigator.tsx
```

## 💾 Database Schema

### Business Model

```typescript
interface Business {
  id: string; // UUID generated on frontend
  name: string; // Business name
  createdAt: Date; // Creation timestamp
  updatedAt: Date; // Last update timestamp
}
```

### Article Model

```typescript
interface Article {
  id: string; // UUID generated on frontend
  name: string; // Article name
  qty: number; // Quantity in stock
  selling_price: number; // Selling price
  business_id: string; // Foreign key to Business
  createdAt: Date; // Creation timestamp
  updatedAt: Date; // Last update timestamp
}
```

## 🔄 Offline-First Architecture

### Local Storage

- **RxDB**: Reactive database with real-time queries
- **SQLite**: Local storage engine for React Native
- **Reactive Updates**: UI automatically updates when data changes

### Synchronization

- **Automatic Sync**: Syncs with CouchDB when network is available
- **Conflict Resolution**: Handles sync conflicts automatically
- **Background Sync**: Continues syncing in the background
- **Retry Logic**: Automatic retry on sync failures

### Network Handling

- **Network Detection**: Real-time network status monitoring
- **Offline Indicators**: Visual indicators for offline/online status
- **Graceful Degradation**: Full functionality when offline

## 🧪 Testing Offline Functionality

### Test Offline CRUD Operations

1. Turn off internet connection
2. Create businesses and articles
3. Edit existing data
4. Delete items
5. Verify all operations work smoothly

### Test Sync Functionality

1. Perform operations while offline
2. Turn on internet connection
3. Observe automatic synchronization
4. Verify data consistency

## 🔧 Development Tools

### Code Quality

- **ESLint**: Code linting with React Native rules
- **Prettier**: Code formatting
- **TypeScript**: Static type checking

### Development Commands

```bash
# Lint code
npm run lint

# Start Metro bundler
npm start

# Run on Android
npm run android

# Run on iOS
npm run ios

# Run tests
npm test
```

## 📱 Building for Production

### Android APK

```bash
cd android
./gradlew assembleRelease
```

The APK will be generated at:
`android/app/build/outputs/apk/release/app-release.apk`

#### Metro Bundler Issues

```bash
npx react-native start --reset-cache
```

#### Android Build Issues

```bash
cd android && ./gradlew clean && cd ..
npm run android
```

#### iOS Build Issues

```bash
cd ios && pod install && cd ..
npm run ios
```

#### Database Issues

- Clear app data and restart
- Check console logs for detailed error messages

## 🏆 Code Quality Features

### TypeScript Implementation

- Full type safety across the application
- Proper interface definitions
- Generic type usage for reusable components

### Architecture Patterns

- Service layer for business logic
- Custom hooks for state management
- Separation of concerns
- Reactive programming with RxJS

### Error Handling

- Try-catch blocks for async operations
- User-friendly error messages
- Graceful error recovery
- Comprehensive validation

### Performance Optimizations

- Reactive queries for efficient data updates
- Proper component memoization
- Optimized list rendering
- Efficient network usage

## 📄 License

This project is for educational and demonstration purposes.

## 🤝 Contributing

This is a demonstration project showcasing React Native development skills with offline-first architecture and professional code quality standards.

---

**Built with ❤️ using React Native, TypeScript, RxDB, and modern development practices**
