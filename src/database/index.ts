import { BehaviorSubject, Observable } from 'rxjs';
import { BusinessDocument } from '../types/Business';
import { ArticleDocument } from '../types/Article';

// Mock database storage
class MockDatabase {
  private businesses$ = new BehaviorSubject<BusinessDocument[]>([]);
  private articles$ = new BehaviorSubject<ArticleDocument[]>([]);

  // Business operations
  async insertBusiness(business: BusinessDocument): Promise<void> {
    const current = this.businesses$.value;
    this.businesses$.next([...current, business]);
  }

  async updateBusiness(
    id: string,
    updates: Partial<BusinessDocument>,
  ): Promise<void> {
    const current = this.businesses$.value;
    const index = current.findIndex(b => b.id === id);
    if (index !== -1) {
      current[index] = { ...current[index], ...updates };
      this.businesses$.next([...current]);
    }
  }

  async deleteBusiness(id: string): Promise<void> {
    const current = this.businesses$.value;
    this.businesses$.next(current.filter(b => b.id !== id));
  }

  findBusiness(id: string): Observable<BusinessDocument | null> {
    return new Observable(subscriber => {
      const subscription = this.businesses$.subscribe(businesses => {
        const business = businesses.find(b => b.id === id) || null;
        subscriber.next(business);
      });
      return () => subscription.unsubscribe();
    });
  }

  findAllBusinesses(): Observable<BusinessDocument[]> {
    return this.businesses$.asObservable();
  }

  // Article operations
  async insertArticle(article: ArticleDocument): Promise<void> {
    const current = this.articles$.value;
    this.articles$.next([...current, article]);
  }

  async updateArticle(
    id: string,
    updates: Partial<ArticleDocument>,
  ): Promise<void> {
    const current = this.articles$.value;
    const index = current.findIndex(a => a.id === id);
    if (index !== -1) {
      current[index] = { ...current[index], ...updates };
      this.articles$.next([...current]);
    }
  }

  async deleteArticle(id: string): Promise<void> {
    const current = this.articles$.value;
    this.articles$.next(current.filter(a => a.id !== id));
  }

  findArticle(id: string): Observable<ArticleDocument | null> {
    return new Observable(subscriber => {
      const subscription = this.articles$.subscribe(articles => {
        const article = articles.find(a => a.id === id) || null;
        subscriber.next(article);
      });
      return () => subscription.unsubscribe();
    });
  }

  findAllArticles(): Observable<ArticleDocument[]> {
    return this.articles$.asObservable();
  }

  findArticlesByBusiness(businessId: string): Observable<ArticleDocument[]> {
    return new Observable(subscriber => {
      const subscription = this.articles$.subscribe(articles => {
        const filtered = articles.filter(a => a.business_id === businessId);
        subscriber.next(filtered);
      });
      return () => subscription.unsubscribe();
    });
  }

  async destroy(): Promise<void> {
    this.businesses$.complete();
    this.articles$.complete();
  }
}

let database: MockDatabase | null = null;

export const initializeDatabase = async (): Promise<MockDatabase> => {
  if (database) {
    return database;
  }

  try {
    database = new MockDatabase();
    return database;
  } catch (error) {
    console.error('Failed to initialize database:', error);
    throw error;
  }
};

export const getDatabase = (): MockDatabase => {
  if (!database) {
    throw new Error(
      'Database not initialized. Call initializeDatabase() first.',
    );
  }
  return database;
};

export const closeDatabase = async (): Promise<void> => {
  if (database) {
    await database.destroy();
    database = null;
  }
};
