import { BehaviorSubject, Observable } from 'rxjs';
import { BusinessDocument } from '../types/Business';
import { ArticleDocument } from '../types/Article';

type Database = {
  insertBusiness(business: BusinessDocument): Promise<void>;
  updateBusiness(id: string, updates: Partial<BusinessDocument>): Promise<void>;
  deleteBusiness(id: string): Promise<void>;
  findBusiness(id: string): Observable<BusinessDocument | null>;
  findAllBusinesses(): Observable<BusinessDocument[]>;

  insertArticle(article: ArticleDocument): Promise<void>;
  updateArticle(id: string, updates: Partial<ArticleDocument>): Promise<void>;
  deleteArticle(id: string): Promise<void>;
  findArticle(id: string): Observable<ArticleDocument | null>;
  findAllArticles(): Observable<ArticleDocument[]>;
  findArticlesByBusiness(businessId: string): Observable<ArticleDocument[]>;

  destroy(): Promise<void>;
};

// Factory function instead of class
function createDatabase(): Database {
  const businesses$ = new BehaviorSubject<BusinessDocument[]>([]);
  const articles$ = new BehaviorSubject<ArticleDocument[]>([]);

  return {
    // Business operations
    async insertBusiness(business) {
      businesses$.next([...businesses$.value, business]);
    },

    async updateBusiness(id, updates) {
      const current = businesses$.value;
      const index = current.findIndex(b => b.id === id);
      if (index !== -1) {
        current[index] = { ...current[index], ...updates };
        businesses$.next([...current]);
      }
    },

    async deleteBusiness(id) {
      businesses$.next(businesses$.value.filter(b => b.id !== id));
    },

    findBusiness(id) {
      return new Observable(subscriber => {
        const subscription = businesses$.subscribe(businesses => {
          const business = businesses.find(b => b.id === id) || null;
          subscriber.next(business);
        });
        return () => subscription.unsubscribe();
      });
    },

    findAllBusinesses() {
      return businesses$.asObservable();
    },

    // Article operations
    async insertArticle(article) {
      articles$.next([...articles$.value, article]);
    },

    async updateArticle(id, updates) {
      const current = articles$.value;
      const index = current.findIndex(a => a.id === id);
      if (index !== -1) {
        current[index] = { ...current[index], ...updates };
        articles$.next([...current]);
      }
    },

    async deleteArticle(id) {
      articles$.next(articles$.value.filter(a => a.id !== id));
    },

    findArticle(id) {
      return new Observable(subscriber => {
        const subscription = articles$.subscribe(articles => {
          const article = articles.find(a => a.id === id) || null;
          subscriber.next(article);
        });
        return () => subscription.unsubscribe();
      });
    },

    findAllArticles() {
      return articles$.asObservable();
    },

    findArticlesByBusiness(businessId) {
      return new Observable(subscriber => {
        const subscription = articles$.subscribe(articles => {
          const filtered = articles.filter(a => a.business_id === businessId);
          subscriber.next(filtered);
        });
        return () => subscription.unsubscribe();
      });
    },

    async destroy() {
      businesses$.complete();
      articles$.complete();
    },
  };
}

let database: Database | null = null;

export const initializeDatabase = async (): Promise<Database> => {
  if (database) return database;
  database = createDatabase();
  return database;
};

export const getDatabase = (): Database => {
  if (!database) {
    throw new Error(
      'Database not initialized. Call initializeDatabase() first.',
    );
  }
  return database;
};

export const closeDatabase = async (): Promise<void> => {
  if (database) {
    await database.destroy();
    database = null;
  }
};
