import { createRxDatabase, addRxPlugin } from 'rxdb';
import { RxDBDevModePlugin } from 'rxdb/plugins/dev-mode';
import { RxDBQueryBuilderPlugin } from 'rxdb/plugins/query-builder';
import { RxDBJsonDumpPlugin } from 'rxdb/plugins/json-dump';

// Import PouchDB for React Native SQLite
import PouchDB from 'pouchdb-core';
const PouchDBAdapterSqlite = require('pouchdb-adapter-react-native-sqlite');

// Add the SQLite adapter to PouchDB
PouchDB.plugin(PouchDBAdapterSqlite);

import { AppDatabase } from '../types/Database';
import { businessSchema } from './schemas/business.schema';
import { articleSchema } from './schemas/article.schema';
import { DATABASE_NAME } from '../utils/constants';

// Add RxDB plugins
if (__DEV__) {
  addRxPlugin(RxDBDevModePlugin);
}
addRxPlugin(RxDBQueryBuilderPlugin);
addRxPlugin(RxDBJsonDumpPlugin);

// Note: PouchDB adapters are not RxDB plugins - they are used directly as storage adapters

let database: AppDatabase | null = null;

export const initializeDatabase = async (): Promise<AppDatabase> => {
  if (database) {
    return database;
  }

  try {
    console.log('Initializing RxDB database...');

    // Create database with PouchDB storage
    database = await createRxDatabase<AppDatabase>({
      name: DATABASE_NAME,
      storage: PouchDB.defaults({
        adapter: 'react-native-sqlite',
      }),
      password: undefined, // Add password for encryption if needed
      multiInstance: false,
      eventReduce: true,
      ignoreDuplicate: true,
    });

    console.log('Database created, adding collections...');

    // Add collections
    await database.addCollections({
      businesses: {
        schema: businessSchema,
        methods: {
          // Custom methods can be added here
        },
      },
      articles: {
        schema: articleSchema,
        methods: {
          // Custom methods can be added here
        },
      },
    });

    console.log('RxDB database initialized successfully');
    return database;
  } catch (error) {
    console.error('Failed to initialize database:', error);
    throw error;
  }
};

export const getDatabase = (): AppDatabase => {
  if (!database) {
    throw new Error(
      'Database not initialized. Call initializeDatabase() first.',
    );
  }
  return database;
};

export const closeDatabase = async (): Promise<void> => {
  if (database) {
    await database.remove();
    database = null;
  }
};
