import React from 'react';
import { NavigationContainer } from '@react-navigation/native';
import { createStackNavigator } from '@react-navigation/stack';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';

// Import actual screens
import BusinessListScreen from '../screens/BusinessListScreen';
import BusinessFormScreen from '../screens/BusinessFormScreen';
import ArticleListScreen from '../screens/ArticleListScreen';
import ArticleFormScreen from '../screens/ArticleFormScreen';

export type RootStackParamList = {
  Main: undefined;
  BusinessForm: { businessId?: string };
  ArticleForm: { articleId?: string; businessId?: string };
};

export type TabParamList = {
  Businesses: undefined;
  Articles: undefined;
};

const Stack = createStackNavigator<RootStackParamList>();
const Tab = createBottomTabNavigator<TabParamList>();

const TabNavigator = () => {
  return (
    <Tab.Navigator
      screenOptions={{
        tabBarActiveTintColor: '#007AFF',
        tabBarInactiveTintColor: '#8E8E93',
        tabBarStyle: {
          backgroundColor: '#FFFFFF',
          borderTopColor: '#C6C6C8',
          borderTopWidth: 1,
        },
        headerStyle: {
          backgroundColor: '#FFFFFF',
          shadowColor: '#000',
          shadowOffset: {
            width: 0,
            height: 1,
          },
          shadowOpacity: 0.1,
          shadowRadius: 2,
          elevation: 2,
        },
        headerTitleStyle: {
          fontSize: 18,
          fontWeight: '600',
          color: '#000000',
        },
      }}
    >
      <Tab.Screen
        name="Businesses"
        component={BusinessListScreen}
        options={{
          title: 'Businesses',
          tabBarLabel: 'Businesses',
        }}
      />
      <Tab.Screen
        name="Articles"
        component={ArticleListScreen}
        options={{
          title: 'Articles',
          tabBarLabel: 'Articles',
        }}
      />
    </Tab.Navigator>
  );
};

const AppNavigator = () => {
  return (
    <NavigationContainer>
      <Stack.Navigator
        screenOptions={{
          headerStyle: {
            backgroundColor: '#FFFFFF',
            shadowColor: '#000',
            shadowOffset: {
              width: 0,
              height: 1,
            },
            shadowOpacity: 0.1,
            shadowRadius: 2,
            elevation: 2,
          },
          headerTitleStyle: {
            fontSize: 18,
            fontWeight: '600',
            color: '#000000',
          },
          headerTintColor: '#007AFF',
        }}
      >
        <Stack.Screen
          name="Main"
          component={TabNavigator}
          options={{ headerShown: false }}
        />
        <Stack.Screen
          name="BusinessForm"
          component={BusinessFormScreen}
          options={{
            title: 'Add Business',
            presentation: 'modal',
          }}
        />
        <Stack.Screen
          name="ArticleForm"
          component={ArticleFormScreen}
          options={{
            title: 'Add Article',
            presentation: 'modal',
          }}
        />
      </Stack.Navigator>
    </NavigationContainer>
  );
};

export default AppNavigator;

