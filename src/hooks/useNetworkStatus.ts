import { useState, useEffect } from 'react';
import { networkService, NetworkState } from '../services/NetworkService';

export const useNetworkStatus = () => {
  const [networkState, setNetworkState] = useState<NetworkState>({
    isConnected: false,
    type: null,
    isInternetReachable: null,
  });

  useEffect(() => {
    // Get initial state
    setNetworkState(networkService.getCurrentNetworkState());

    // Subscribe to changes
    const subscription = networkService.getNetworkState$().subscribe({
      next: (state) => {
        setNetworkState(state);
      },
      error: (error) => {
        console.error('Network status error:', error);
      },
    });

    return () => subscription.unsubscribe();
  }, []);

  const isOnline = networkState.isConnected && networkState.isInternetReachable !== false;

  return {
    ...networkState,
    isOnline,
  };
};

