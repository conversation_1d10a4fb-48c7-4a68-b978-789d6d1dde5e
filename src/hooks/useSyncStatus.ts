import { useState, useEffect } from 'react';
import { syncService } from '../services/SyncService';
import { SyncStatus } from '../types/Database';

export const useSyncStatus = () => {
  const [syncStatus, setSyncStatus] = useState<SyncStatus>({
    isOnline: false,
    isSyncing: false,
  });

  useEffect(() => {
    // Get initial state
    setSyncStatus(syncService.getCurrentSyncStatus());

    // Subscribe to changes
    const subscription = syncService.getSyncStatus$().subscribe({
      next: status => {
        setSyncStatus(status);
      },
      error: error => {
        console.error('Sync status error:', error);
      },
    });

    return () => subscription.unsubscribe();
  }, []);

  const forceSync = async (): Promise<void> => {
    try {
      await syncService.forceSync();
    } catch (error) {
      console.error('Force sync failed:', error);
      throw error;
    }
  };

  return {
    ...syncStatus,
    forceSync,
  };
};
