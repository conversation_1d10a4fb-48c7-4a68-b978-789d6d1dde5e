import NetInfo from '@react-native-community/netinfo';
import { BehaviorSubject, Observable } from 'rxjs';

export interface NetworkState {
  isConnected: boolean;
  type: string | null;
  isInternetReachable: boolean | null;
}

class NetworkService {
  private networkState$ = new BehaviorSubject<NetworkState>({
    isConnected: false,
    type: null,
    isInternetReachable: null,
  });

  private unsubscribe: (() => void) | null = null;

  constructor() {
    this.initialize();
  }

  private initialize(): void {
    // Subscribe to network state changes
    this.unsubscribe = NetInfo.addEventListener(state => {
      const networkState: NetworkState = {
        isConnected: state.isConnected ?? false,
        type: state.type,
        isInternetReachable: state.isInternetReachable,
      };
      
      console.log('Network state changed:', networkState);
      this.networkState$.next(networkState);
    });

    // Get initial network state
    NetInfo.fetch().then(state => {
      const networkState: NetworkState = {
        isConnected: state.isConnected ?? false,
        type: state.type,
        isInternetReachable: state.isInternetReachable,
      };
      
      console.log('Initial network state:', networkState);
      this.networkState$.next(networkState);
    });
  }

  public getNetworkState$(): Observable<NetworkState> {
    return this.networkState$.asObservable();
  }

  public getCurrentNetworkState(): NetworkState {
    return this.networkState$.value;
  }

  public isOnline(): boolean {
    const state = this.getCurrentNetworkState();
    return state.isConnected && state.isInternetReachable !== false;
  }

  public destroy(): void {
    if (this.unsubscribe) {
      this.unsubscribe();
      this.unsubscribe = null;
    }
    this.networkState$.complete();
  }
}

export const networkService = new NetworkService();

