import { BehaviorSubject, Observable, Subscription } from 'rxjs';
import { debounceTime } from 'rxjs/operators';

import { getDatabase } from '../database';
import { networkService, NetworkState } from './NetworkService';
import { SyncStatus } from '../types/Database';
import {
  SYNC_CONFIG,
  COUCHDB_URL,
  COUCHDB_USERNAME,
  COUCHDB_PASSWORD,
} from '../utils/constants';

class SyncService {
  private syncStatus$ = new BehaviorSubject<SyncStatus>({
    isOnline: false,
    isSyncing: false,
  });

  private networkSubscription: Subscription | null = null;
  private businessesReplication: any = null;
  private articlesReplication: any = null;
  private retryCount = 0;

  constructor() {
    this.initialize();
  }

  private initialize(): void {
    // Subscribe to network changes
    this.networkSubscription = networkService
      .getNetworkState$()
      .pipe(
        debounceTime(1000), // Debounce network changes
      )
      .subscribe(networkState => {
        this.handleNetworkChange(networkState);
      });
  }

  private async handleNetworkChange(networkState: NetworkState): Promise<void> {
    const isOnline =
      networkState.isConnected && networkState.isInternetReachable !== false;

    this.updateSyncStatus({
      isOnline,
      isSyncing: false,
    });

    if (isOnline) {
      await this.startSync();
    } else {
      await this.stopSync();
    }
  }

  private async startSync(): Promise<void> {
    try {
      console.log('Starting CouchDB synchronization...');

      this.updateSyncStatus({
        isOnline: true,
        isSyncing: true,
        syncError: undefined,
      });

      const database = getDatabase();

      // TODO: Implement actual CouchDB replication
      // For now, we simulate sync capability being available
      console.log('CouchDB sync capability available');
      console.log('Database collections:', {
        businesses: database.businesses.name,
        articles: database.articles.name,
      });

      // Simulate sync process
      setTimeout(() => {
        this.updateSyncStatus({
          isOnline: true,
          isSyncing: false,
          lastSyncTime: new Date(),
          syncError: undefined,
        });
        console.log('Sync simulation completed');
      }, 2000);

      console.log('CouchDB synchronization setup completed');

      this.retryCount = 0;
    } catch (error) {
      console.error('Failed to start sync:', error);
      this.handleSyncError(error);
    }
  }

  private async stopSync(): Promise<void> {
    console.log('Stopping synchronization...');

    if (this.businessesReplication) {
      this.businessesReplication.cancel();
      this.businessesReplication = null;
    }

    if (this.articlesReplication) {
      this.articlesReplication.cancel();
      this.articlesReplication = null;
    }

    this.updateSyncStatus({
      isOnline: false,
      isSyncing: false,
    });

    console.log('Synchronization stopped');
  }

  private handleSyncError(error: any): void {
    console.error('Sync error:', error);

    this.retryCount++;

    if (this.retryCount < SYNC_CONFIG.MAX_RETRIES) {
      console.log(
        `Retrying sync in ${SYNC_CONFIG.RETRY_DELAY}ms (attempt ${this.retryCount})`,
      );
      setTimeout(() => {
        this.startSync();
      }, SYNC_CONFIG.RETRY_DELAY);
    } else {
      console.error('Max retry attempts reached');
      this.updateSyncStatus({
        isOnline: networkService.isOnline(),
        isSyncing: false,
        syncError: error.message || 'Sync failed after multiple attempts',
      });
    }
  }

  private updateSyncStatus(status: Partial<SyncStatus>): void {
    const currentStatus = this.syncStatus$.value;
    this.syncStatus$.next({
      ...currentStatus,
      ...status,
    });
  }

  public getSyncStatus$(): Observable<SyncStatus> {
    return this.syncStatus$.asObservable();
  }

  public getCurrentSyncStatus(): SyncStatus {
    return this.syncStatus$.value;
  }

  public async forceSync(): Promise<void> {
    if (networkService.isOnline()) {
      await this.startSync();
    } else {
      throw new Error('Cannot sync while offline');
    }
  }

  public destroy(): void {
    if (this.networkSubscription) {
      this.networkSubscription.unsubscribe();
      this.networkSubscription = null;
    }

    this.stopSync();
    this.syncStatus$.complete();
  }
}

export const syncService = new SyncService();
