import { BehaviorSubject, Observable, Subscription } from 'rxjs';
import { debounceTime } from 'rxjs/operators';

import { networkService, NetworkState } from './NetworkService';
import { SyncStatus } from '../types/Database';
import { SYNC_CONFIG } from '../utils/constants';

class SyncService {
  private syncStatus$ = new BehaviorSubject<SyncStatus>({
    isOnline: false,
    isSyncing: false,
  });

  private networkSubscription: Subscription | null = null;
  private retryCount = 0;

  constructor() {
    this.initialize();
  }

  private initialize(): void {
    // Subscribe to network changes
    this.networkSubscription = networkService
      .getNetworkState$()
      .pipe(
        debounceTime(1000), // Debounce network changes
      )
      .subscribe((networkState) => {
        this.handleNetworkChange(networkState);
      });
  }

  private async handleNetworkChange(networkState: NetworkState): Promise<void> {
    const isOnline = networkState.isConnected && networkState.isInternetReachable !== false;
    
    this.updateSyncStatus({
      isOnline,
      isSyncing: false,
    });

    if (isOnline) {
      await this.startSync();
    } else {
      await this.stopSync();
    }
  }

  private async startSync(): Promise<void> {
    try {
      console.log('Network is online - sync capability available');
      
      this.updateSyncStatus({
        isOnline: true,
        isSyncing: false, // Set to true when actual sync implementation is added
        lastSyncTime: new Date(),
        syncError: undefined,
      });

      // TODO: Implement actual CouchDB sync when needed
      // For now, we just track the online status
      
      this.retryCount = 0;
      console.log('Sync status updated successfully');
    } catch (error) {
      console.error('Failed to update sync status:', error);
      this.handleSyncError(error);
    }
  }

  private async stopSync(): Promise<void> {
    try {
      console.log('Network is offline - sync stopped');

      this.updateSyncStatus({
        isOnline: false,
        isSyncing: false,
      });

      console.log('Sync stopped');
    } catch (error) {
      console.error('Failed to stop sync:', error);
    }
  }

  private handleSyncError(error: any): void {
    console.error('Sync error:', error);
    
    this.retryCount++;
    
    if (this.retryCount < SYNC_CONFIG.MAX_RETRIES) {
      console.log(`Retrying sync in ${SYNC_CONFIG.RETRY_DELAY}ms (attempt ${this.retryCount})`);
      setTimeout(() => {
        this.startSync();
      }, SYNC_CONFIG.RETRY_DELAY);
    } else {
      console.error('Max retry attempts reached');
      this.updateSyncStatus({
        isOnline: networkService.isOnline(),
        isSyncing: false,
        syncError: error.message || 'Sync failed after multiple attempts',
      });
    }
  }

  private updateSyncStatus(status: Partial<SyncStatus>): void {
    const currentStatus = this.syncStatus$.value;
    this.syncStatus$.next({
      ...currentStatus,
      ...status,
    });
  }

  public getSyncStatus$(): Observable<SyncStatus> {
    return this.syncStatus$.asObservable();
  }

  public getCurrentSyncStatus(): SyncStatus {
    return this.syncStatus$.value;
  }

  public async forcSync(): Promise<void> {
    if (networkService.isOnline()) {
      await this.startSync();
    } else {
      throw new Error('Cannot sync while offline');
    }
  }

  public destroy(): void {
    if (this.networkSubscription) {
      this.networkSubscription.unsubscribe();
      this.networkSubscription = null;
    }

    this.syncStatus$.complete();
  }
}

export const syncService = new SyncService();

