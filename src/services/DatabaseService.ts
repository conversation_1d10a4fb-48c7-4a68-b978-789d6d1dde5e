import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';

import { getDatabase } from '../database';
import { Business, BusinessInput, BusinessDocument } from '../types/Business';
import { Article, ArticleInput, ArticleDocument } from '../types/Article';
import { generateId } from '../utils/helpers';
import { validateBusiness, validateArticle } from '../utils/validation';

class DatabaseService {
  // Business CRUD Operations
  async createBusiness(input: BusinessInput): Promise<Business> {
    const errors = validateBusiness(input);
    if (errors.length > 0) {
      throw new Error(
        `Validation failed: ${errors.map(e => e.message).join(', ')}`,
      );
    }

    const database = getDatabase();
    const now = new Date();

    const business: Business = {
      id: generateId(),
      name: input.name.trim(),
      createdAt: now,
      updatedAt: now,
    };

    const businessDoc: BusinessDocument = {
      ...business,
      _id: business.id,
    };

    try {
      await database.insertBusiness(businessDoc);
      return business;
    } catch (error) {
      console.error('Failed to create business:', error);
      throw new Error('Failed to create business');
    }
  }

  async updateBusiness(id: string, input: BusinessInput): Promise<Business> {
    const errors = validateBusiness(input);
    if (errors.length > 0) {
      throw new Error(
        `Validation failed: ${errors.map(e => e.message).join(', ')}`,
      );
    }

    const database = getDatabase();

    try {
      const updatedBusiness: Partial<Business> = {
        name: input.name.trim(),
        updatedAt: new Date(),
      };

      await database.updateBusiness(id, updatedBusiness);

      // Return the updated business
      const result: Business = {
        id,
        name: updatedBusiness.name!,
        createdAt: new Date(), // This would come from the actual stored data
        updatedAt: updatedBusiness.updatedAt!,
      };

      console.log('Business updated:', result);
      return result;
    } catch (error) {
      console.error('Failed to update business:', error);
      throw new Error('Failed to update business');
    }
  }

  async deleteBusiness(id: string): Promise<void> {
    const database = getDatabase();

    try {
      // Check if business has articles
      const articles = await new Promise<ArticleDocument[]>(resolve => {
        const subscription = database
          .findArticlesByBusiness(id)
          .subscribe(articles => {
            subscription.unsubscribe();
            resolve(articles);
          });
      });

      if (articles.length > 0) {
        throw new Error('Cannot delete business with existing articles');
      }

      await database.deleteBusiness(id);
      console.log('Business deleted:', id);
    } catch (error) {
      console.error('Failed to delete business:', error);
      throw error;
    }
  }

  getBusiness(id: string): Observable<Business | null> {
    const database = getDatabase();
    return database.findBusiness(id).pipe(
      map(doc =>
        doc
          ? {
              id: doc.id,
              name: doc.name,
              createdAt: doc.createdAt,
              updatedAt: doc.updatedAt,
            }
          : null,
      ),
    );
  }

  getAllBusinesses(): Observable<Business[]> {
    const database = getDatabase();
    return database.findAllBusinesses().pipe(
      map(docs =>
        docs
          .map(doc => ({
            id: doc.id,
            name: doc.name,
            createdAt: doc.createdAt,
            updatedAt: doc.updatedAt,
          }))
          .sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime()),
      ),
    );
  }

  // Article CRUD Operations
  async createArticle(input: ArticleInput): Promise<Article> {
    const errors = validateArticle(input);
    if (errors.length > 0) {
      throw new Error(
        `Validation failed: ${errors.map(e => e.message).join(', ')}`,
      );
    }

    const database = getDatabase();

    // Verify business exists
    const businessExists = await new Promise<boolean>(resolve => {
      const subscription = database
        .findBusiness(input.business_id)
        .subscribe(business => {
          subscription.unsubscribe();
          resolve(business !== null);
        });
    });

    if (!businessExists) {
      throw new Error('Business not found');
    }

    const now = new Date();

    const article: Article = {
      id: generateId(),
      name: input.name.trim(),
      qty: input.qty,
      selling_price: input.selling_price,
      business_id: input.business_id,
      createdAt: now,
      updatedAt: now,
    };

    const articleDoc: ArticleDocument = {
      ...article,
      _id: article.id,
    };

    try {
      await database.insertArticle(articleDoc);
      console.log('Article created:', article);
      return article;
    } catch (error) {
      console.error('Failed to create article:', error);
      throw new Error('Failed to create article');
    }
  }

  async updateArticle(id: string, input: ArticleInput): Promise<Article> {
    const errors = validateArticle(input);
    if (errors.length > 0) {
      throw new Error(
        `Validation failed: ${errors.map(e => e.message).join(', ')}`,
      );
    }

    const database = getDatabase();

    try {
      // Verify business exists
      const businessExists = await new Promise<boolean>(resolve => {
        const subscription = database
          .findBusiness(input.business_id)
          .subscribe(business => {
            subscription.unsubscribe();
            resolve(business !== null);
          });
      });

      if (!businessExists) {
        throw new Error('Business not found');
      }

      const updatedArticle: Partial<Article> = {
        name: input.name.trim(),
        qty: input.qty,
        selling_price: input.selling_price,
        business_id: input.business_id,
        updatedAt: new Date(),
      };

      await database.updateArticle(id, updatedArticle);

      // Return the updated article
      const result: Article = {
        id,
        name: updatedArticle.name!,
        qty: updatedArticle.qty!,
        selling_price: updatedArticle.selling_price!,
        business_id: updatedArticle.business_id!,
        createdAt: new Date(), // This would come from the actual stored data
        updatedAt: updatedArticle.updatedAt!,
      };

      console.log('Article updated:', result);
      return result;
    } catch (error) {
      console.error('Failed to update article:', error);
      throw new Error('Failed to update article');
    }
  }

  async deleteArticle(id: string): Promise<void> {
    const database = getDatabase();

    try {
      await database.deleteArticle(id);
      console.log('Article deleted:', id);
    } catch (error) {
      console.error('Failed to delete article:', error);
      throw error;
    }
  }

  getArticle(id: string): Observable<Article | null> {
    const database = getDatabase();
    return database.findArticle(id).pipe(
      map(doc =>
        doc
          ? {
              id: doc.id,
              name: doc.name,
              qty: doc.qty,
              selling_price: doc.selling_price,
              business_id: doc.business_id,
              createdAt: doc.createdAt,
              updatedAt: doc.updatedAt,
            }
          : null,
      ),
    );
  }

  getAllArticles(): Observable<Article[]> {
    const database = getDatabase();
    return database.findAllArticles().pipe(
      map(docs =>
        docs
          .map(doc => ({
            id: doc.id,
            name: doc.name,
            qty: doc.qty,
            selling_price: doc.selling_price,
            business_id: doc.business_id,
            createdAt: doc.createdAt,
            updatedAt: doc.updatedAt,
          }))
          .sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime()),
      ),
    );
  }

  getArticlesByBusiness(businessId: string): Observable<Article[]> {
    const database = getDatabase();
    return database.findArticlesByBusiness(businessId).pipe(
      map(docs =>
        docs
          .map(doc => ({
            id: doc.id,
            name: doc.name,
            qty: doc.qty,
            selling_price: doc.selling_price,
            business_id: doc.business_id,
            createdAt: doc.createdAt,
            updatedAt: doc.updatedAt,
          }))
          .sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime()),
      ),
    );
  }

  searchBusinesses(query: string): Observable<Business[]> {
    return this.getAllBusinesses().pipe(
      map(businesses =>
        businesses.filter(business =>
          business.name.toLowerCase().includes(query.toLowerCase()),
        ),
      ),
    );
  }

  searchArticles(query: string): Observable<Article[]> {
    return this.getAllArticles().pipe(
      map(articles =>
        articles.filter(article =>
          article.name.toLowerCase().includes(query.toLowerCase()),
        ),
      ),
    );
  }
}

export const databaseService = new DatabaseService();
