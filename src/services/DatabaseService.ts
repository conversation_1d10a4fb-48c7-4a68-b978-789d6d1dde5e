import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';

import { getDatabase } from '../database';
import { Business, BusinessInput, BusinessDocument } from '../types/Business';
import { Article, ArticleInput, ArticleDocument } from '../types/Article';
import { generateId } from '../utils/helpers';
import { validateBusiness, validateArticle } from '../utils/validation';

class DatabaseService {
  // Business CRUD Operations
  async createBusiness(input: BusinessInput): Promise<Business> {
    const errors = validateBusiness(input);
    if (errors.length > 0) {
      throw new Error(
        `Validation failed: ${errors.map(e => e.message).join(', ')}`,
      );
    }

    const database = getDatabase();
    const now = new Date();
    const id = generateId();

    const businessDoc: BusinessDocument = {
      id,
      _id: id,
      name: input.name.trim(),
      createdAt: now,
      updatedAt: now,
    };

    try {
      await database.businesses.insert(businessDoc);
      console.log('Business created:', businessDoc);

      return {
        id: businessDoc.id,
        name: businessDoc.name,
        createdAt: businessDoc.createdAt,
        updatedAt: businessDoc.updatedAt,
      };
    } catch (error) {
      console.error('Failed to create business:', error);
      throw new Error('Failed to create business');
    }
  }

  async updateBusiness(id: string, input: BusinessInput): Promise<Business> {
    const errors = validateBusiness(input);
    if (errors.length > 0) {
      throw new Error(
        `Validation failed: ${errors.map(e => e.message).join(', ')}`,
      );
    }

    const database = getDatabase();

    try {
      const businessDoc = await database.businesses.findOne(id).exec();
      if (!businessDoc) {
        throw new Error('Business not found');
      }

      await businessDoc.update({
        $set: {
          name: input.name.trim(),
          updatedAt: new Date(),
        },
      });

      return {
        id: businessDoc.id,
        name: businessDoc.name,
        createdAt: businessDoc.createdAt,
        updatedAt: businessDoc.updatedAt,
      };
    } catch (error) {
      console.error('Failed to update business:', error);
      throw error;
    }
  }

  async deleteBusiness(id: string): Promise<void> {
    const database = getDatabase();

    try {
      // Check if business has articles
      const articles = await database.articles
        .find({ selector: { business_id: id } })
        .exec();
      const articlesCount = articles.length;

      if (articlesCount > 0) {
        throw new Error('Cannot delete business with existing articles');
      }

      const businessDoc = await database.businesses.findOne(id).exec();
      if (!businessDoc) {
        throw new Error('Business not found');
      }

      await businessDoc.remove();
      console.log('Business deleted:', id);
    } catch (error) {
      console.error('Failed to delete business:', error);
      throw error;
    }
  }

  getBusiness(id: string): Observable<Business | null> {
    const database = getDatabase();
    return database.businesses.findOne(id).$.pipe(
      map(doc =>
        doc
          ? {
              id: doc.id,
              name: doc.name,
              createdAt: doc.createdAt,
              updatedAt: doc.updatedAt,
            }
          : null,
      ),
    );
  }

  getAllBusinesses(): Observable<Business[]> {
    const database = getDatabase();
    return database.businesses
      .find()
      .sort({ createdAt: 'desc' })
      .$.pipe(
        map(docs =>
          docs.map(doc => ({
            id: doc.id,
            name: doc.name,
            createdAt: doc.createdAt,
            updatedAt: doc.updatedAt,
          })),
        ),
      );
  }

  // Article CRUD Operations
  async createArticle(input: ArticleInput): Promise<Article> {
    const errors = validateArticle(input);
    if (errors.length > 0) {
      throw new Error(
        `Validation failed: ${errors.map(e => e.message).join(', ')}`,
      );
    }

    const database = getDatabase();

    // Verify business exists
    const businessDoc = await database.businesses
      .findOne(input.business_id)
      .exec();
    if (!businessDoc) {
      throw new Error('Business not found');
    }

    const now = new Date();
    const id = generateId();

    const articleDoc: ArticleDocument = {
      id,
      _id: id,
      name: input.name.trim(),
      qty: input.qty,
      selling_price: input.selling_price,
      business_id: input.business_id,
      createdAt: now,
      updatedAt: now,
    };

    try {
      await database.articles.insert(articleDoc);
      console.log('Article created:', articleDoc);

      return {
        id: articleDoc.id,
        name: articleDoc.name,
        qty: articleDoc.qty,
        selling_price: articleDoc.selling_price,
        business_id: articleDoc.business_id,
        createdAt: articleDoc.createdAt,
        updatedAt: articleDoc.updatedAt,
      };
    } catch (error) {
      console.error('Failed to create article:', error);
      throw new Error('Failed to create article');
    }
  }

  async updateArticle(id: string, input: ArticleInput): Promise<Article> {
    const errors = validateArticle(input);
    if (errors.length > 0) {
      throw new Error(
        `Validation failed: ${errors.map(e => e.message).join(', ')}`,
      );
    }

    const database = getDatabase();

    try {
      // Verify business exists
      const businessDoc = await database.businesses
        .findOne(input.business_id)
        .exec();
      if (!businessDoc) {
        throw new Error('Business not found');
      }

      const articleDoc = await database.articles.findOne(id).exec();
      if (!articleDoc) {
        throw new Error('Article not found');
      }

      await articleDoc.update({
        $set: {
          name: input.name.trim(),
          qty: input.qty,
          selling_price: input.selling_price,
          business_id: input.business_id,
          updatedAt: new Date(),
        },
      });

      return {
        id: articleDoc.id,
        name: articleDoc.name,
        qty: articleDoc.qty,
        selling_price: articleDoc.selling_price,
        business_id: articleDoc.business_id,
        createdAt: articleDoc.createdAt,
        updatedAt: articleDoc.updatedAt,
      };
    } catch (error) {
      console.error('Failed to update article:', error);
      throw error;
    }
  }

  async deleteArticle(id: string): Promise<void> {
    const database = getDatabase();

    try {
      const articleDoc = await database.articles.findOne(id).exec();
      if (!articleDoc) {
        throw new Error('Article not found');
      }

      await articleDoc.remove();
      console.log('Article deleted:', id);
    } catch (error) {
      console.error('Failed to delete article:', error);
      throw error;
    }
  }

  getArticle(id: string): Observable<Article | null> {
    const database = getDatabase();
    return database.articles.findOne(id).$.pipe(
      map(doc =>
        doc
          ? {
              id: doc.id,
              name: doc.name,
              qty: doc.qty,
              selling_price: doc.selling_price,
              business_id: doc.business_id,
              createdAt: doc.createdAt,
              updatedAt: doc.updatedAt,
            }
          : null,
      ),
    );
  }

  getAllArticles(): Observable<Article[]> {
    const database = getDatabase();
    return database.articles
      .find()
      .sort({ createdAt: 'desc' })
      .$.pipe(
        map(docs =>
          docs.map(doc => ({
            id: doc.id,
            name: doc.name,
            qty: doc.qty,
            selling_price: doc.selling_price,
            business_id: doc.business_id,
            createdAt: doc.createdAt,
            updatedAt: doc.updatedAt,
          })),
        ),
      );
  }

  getArticlesByBusiness(businessId: string): Observable<Article[]> {
    const database = getDatabase();
    return database.articles
      .find({ selector: { business_id: businessId } })
      .sort({ createdAt: 'desc' })
      .$.pipe(
        map(docs =>
          docs.map(doc => ({
            id: doc.id,
            name: doc.name,
            qty: doc.qty,
            selling_price: doc.selling_price,
            business_id: doc.business_id,
            createdAt: doc.createdAt,
            updatedAt: doc.updatedAt,
          })),
        ),
      );
  }

  searchBusinesses(query: string): Observable<Business[]> {
    return this.getAllBusinesses().pipe(
      map(businesses =>
        businesses.filter(business =>
          business.name.toLowerCase().includes(query.toLowerCase()),
        ),
      ),
    );
  }

  searchArticles(query: string): Observable<Article[]> {
    return this.getAllArticles().pipe(
      map(articles =>
        articles.filter(article =>
          article.name.toLowerCase().includes(query.toLowerCase()),
        ),
      ),
    );
  }
}

export const databaseService = new DatabaseService();
