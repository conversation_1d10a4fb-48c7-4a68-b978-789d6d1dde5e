import React, { useState } from 'react';
import {
  View,
  Text,
  FlatList,
  TouchableOpacity,
  StyleSheet,
  Alert,
  RefreshControl,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';

import { useBusinesses } from '../hooks/useDatabase';
import { useNetworkStatus } from '../hooks/useNetworkStatus';
import { useSyncStatus } from '../hooks/useSyncStatus';
import { Business } from '../types/Business';
import { RootStackParamList } from '../navigation/AppNavigator';

import LoadingSpinner from '../components/common/LoadingSpinner';
import EmptyState from '../components/common/EmptyState';
import Button from '../components/common/Button';
import { COLORS, SPACING, TYPOGRAPHY } from '../utils/constants';
import { formatDate } from '../utils/helpers';

type NavigationProp = StackNavigationProp<RootStackParamList>;

const BusinessListScreen: React.FC = () => {
  const navigation = useNavigation<NavigationProp>();
  const { businesses, loading, error, deleteBusiness } = useBusinesses();
  const { isOnline } = useNetworkStatus();
  const { isSyncing } = useSyncStatus();
  const [refreshing, setRefreshing] = useState(false);

  const handleCreateBusiness = () => {
    navigation.navigate('BusinessForm', {});
  };

  const handleEditBusiness = (businessId: string) => {
    navigation.navigate('BusinessForm', { businessId });
  };

  const handleDeleteBusiness = (business: Business) => {
    Alert.alert(
      'Delete Business',
      `Are you sure you want to delete "${business.name}"? This action cannot be undone.`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => {
            try {
              await deleteBusiness(business.id);
            } catch (err) {
              Alert.alert(
                'Error',
                err instanceof Error ? err.message : 'Failed to delete business'
              );
            }
          },
        },
      ]
    );
  };

  const onRefresh = async () => {
    setRefreshing(true);
    // The data will automatically refresh through the reactive subscription
    setTimeout(() => setRefreshing(false), 1000);
  };

  const renderBusinessItem = ({ item }: { item: Business }) => (
    <TouchableOpacity
      style={styles.businessItem}
      onPress={() => handleEditBusiness(item.id)}
      activeOpacity={0.7}
    >
      <View style={styles.businessContent}>
        <Text style={styles.businessName}>{item.name}</Text>
        <Text style={styles.businessDate}>
          Created: {formatDate(item.createdAt)}
        </Text>
      </View>
      
      <TouchableOpacity
        style={styles.deleteButton}
        onPress={() => handleDeleteBusiness(item)}
        hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
      >
        <Text style={styles.deleteButtonText}>×</Text>
      </TouchableOpacity>
    </TouchableOpacity>
  );

  const renderHeader = () => (
    <View style={styles.header}>
      <View style={styles.statusContainer}>
        <View style={[styles.statusDot, { backgroundColor: isOnline ? COLORS.SUCCESS : COLORS.ERROR }]} />
        <Text style={styles.statusText}>
          {isOnline ? 'Online' : 'Offline'}
          {isSyncing && ' • Syncing...'}
        </Text>
      </View>
      
      <Button
        title="Add Business"
        onPress={handleCreateBusiness}
        size="medium"
        style={styles.addButton}
      />
    </View>
  );

  if (loading && businesses.length === 0) {
    return <LoadingSpinner message="Loading businesses..." />;
  }

  if (error && businesses.length === 0) {
    return (
      <View style={styles.container}>
        {renderHeader()}
        <EmptyState
          title="Error"
          message={error}
          actionTitle="Retry"
          onAction={onRefresh}
        />
      </View>
    );
  }

  if (businesses.length === 0) {
    return (
      <View style={styles.container}>
        {renderHeader()}
        <EmptyState
          title="No Businesses"
          message="You haven't created any businesses yet. Tap the button above to get started."
          actionTitle="Create First Business"
          onAction={handleCreateBusiness}
        />
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <FlatList
        data={businesses}
        renderItem={renderBusinessItem}
        keyExtractor={(item) => item.id}
        ListHeaderComponent={renderHeader}
        contentContainerStyle={styles.listContent}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            tintColor={COLORS.PRIMARY}
          />
        }
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.BACKGROUND,
  },
  header: {
    padding: SPACING.MD,
    backgroundColor: COLORS.SURFACE,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.BORDER,
  },
  statusContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: SPACING.SM,
  },
  statusDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginRight: SPACING.XS,
  },
  statusText: {
    fontSize: TYPOGRAPHY.FONT_SIZE.SM,
    color: COLORS.TEXT_SECONDARY,
  },
  addButton: {
    alignSelf: 'stretch',
  },
  listContent: {
    flexGrow: 1,
  },
  businessItem: {
    backgroundColor: COLORS.SURFACE,
    marginHorizontal: SPACING.MD,
    marginVertical: SPACING.XS,
    padding: SPACING.MD,
    borderRadius: 8,
    flexDirection: 'row',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  businessContent: {
    flex: 1,
  },
  businessName: {
    fontSize: TYPOGRAPHY.FONT_SIZE.LG,
    fontWeight: TYPOGRAPHY.FONT_WEIGHT.SEMIBOLD,
    color: COLORS.TEXT_PRIMARY,
    marginBottom: SPACING.XS,
  },
  businessDate: {
    fontSize: TYPOGRAPHY.FONT_SIZE.SM,
    color: COLORS.TEXT_SECONDARY,
  },
  deleteButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: COLORS.ERROR,
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: SPACING.SM,
  },
  deleteButtonText: {
    color: COLORS.SURFACE,
    fontSize: 20,
    fontWeight: TYPOGRAPHY.FONT_WEIGHT.BOLD,
    lineHeight: 20,
  },
});

export default BusinessListScreen;

