import React, { useState, useEffect } from 'react';
import {
  View,
  ScrollView,
  StyleSheet,
  Alert,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import { useNavigation, useRoute, RouteProp as NavigationRouteProp } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';

import { useBusinesses, useBusiness } from '../hooks/useDatabase';
import { BusinessInput } from '../types/Business';
import { RootStackParamList } from '../navigation/AppNavigator';

import Input from '../components/common/Input';
import Button from '../components/common/Button';
import LoadingSpinner from '../components/common/LoadingSpinner';
import { COLORS, SPACING } from '../utils/constants';

type NavigationProp = StackNavigationProp<RootStackParamList, 'BusinessForm'>;
type BusinessFormRouteProp = NavigationRouteProp<RootStackParamList, 'BusinessForm'>;

const BusinessFormScreen: React.FC = () => {
  const navigation = useNavigation<NavigationProp>();
  const route = useRoute<BusinessFormRouteProp>();
  const { businessId } = route.params;

  const { createBusiness, updateBusiness } = useBusinesses();
  const { business, loading: businessLoading } = useBusiness(businessId || null);

  const [formData, setFormData] = useState<BusinessInput>({
    name: '',
  });
  const [errors, setErrors] = useState<{ [key: string]: string }>({});
  const [loading, setLoading] = useState(false);

  const isEditing = !!businessId;

  useEffect(() => {
    if (isEditing && business) {
      setFormData({
        name: business.name,
      });
    }
  }, [isEditing, business]);

  const validateForm = (): boolean => {
    const newErrors: { [key: string]: string } = {};

    if (!formData.name.trim()) {
      newErrors.name = 'Business name is required';
    } else if (formData.name.trim().length > 255) {
      newErrors.name = 'Business name must be less than 255 characters';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async () => {
    if (!validateForm()) {
      return;
    }

    setLoading(true);
    setErrors({});

    try {
      if (isEditing && businessId) {
        await updateBusiness(businessId, formData);
        Alert.alert('Success', 'Business updated successfully', [
          { text: 'OK', onPress: () => navigation.goBack() },
        ]);
      } else {
        await createBusiness(formData);
        Alert.alert('Success', 'Business created successfully', [
          { text: 'OK', onPress: () => navigation.goBack() },
        ]);
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'An error occurred';
      Alert.alert('Error', errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    navigation.goBack();
  };

  if (isEditing && businessLoading) {
    return <LoadingSpinner message="Loading business..." />;
  }

  return (
    <KeyboardAvoidingView
      style={styles.container}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
    >
      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        keyboardShouldPersistTaps="handled"
      >
        <View style={styles.form}>
          <Input
            label="Business Name"
            value={formData.name}
            onChangeText={(text) => setFormData({ ...formData, name: text })}
            placeholder="Enter business name"
            error={errors.name}
            required
            autoFocus
            maxLength={255}
          />
        </View>

        <View style={styles.buttonContainer}>
          <Button
            title="Cancel"
            onPress={handleCancel}
            variant="secondary"
            style={styles.cancelButton}
          />
          <Button
            title={isEditing ? 'Update' : 'Create'}
            onPress={handleSubmit}
            loading={loading}
            style={styles.submitButton}
          />
        </View>
      </ScrollView>
    </KeyboardAvoidingView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.BACKGROUND,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
  },
  form: {
    flex: 1,
    padding: SPACING.MD,
  },
  buttonContainer: {
    flexDirection: 'row',
    padding: SPACING.MD,
    paddingTop: 0,
    gap: SPACING.SM,
  },
  cancelButton: {
    flex: 1,
  },
  submitButton: {
    flex: 1,
  },
});

export default BusinessFormScreen;

