import React, { useState } from 'react';
import {
  View,
  Text,
  FlatList,
  TouchableOpacity,
  StyleSheet,
  Alert,
  RefreshControl,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';

import { useArticles, useBusinesses } from '../hooks/useDatabase';
import { useNetworkStatus } from '../hooks/useNetworkStatus';
import { useSyncStatus } from '../hooks/useSyncStatus';
import { Article } from '../types/Article';
import { RootStackParamList } from '../navigation/AppNavigator';

import LoadingSpinner from '../components/common/LoadingSpinner';
import EmptyState from '../components/common/EmptyState';
import Button from '../components/common/Button';
import { COLORS, SPACING, TYPOGRAPHY } from '../utils/constants';
import { formatDate, formatCurrency } from '../utils/helpers';

type NavigationProp = StackNavigationProp<RootStackParamList>;

const ArticleListScreen: React.FC = () => {
  const navigation = useNavigation<NavigationProp>();
  const { articles, loading, error, deleteArticle } = useArticles();
  const { businesses } = useBusinesses();
  const { isOnline } = useNetworkStatus();
  const { isSyncing } = useSyncStatus();
  const [refreshing, setRefreshing] = useState(false);

  const handleCreateArticle = () => {
    if (businesses.length === 0) {
      Alert.alert(
        'No Businesses',
        'You need to create at least one business before adding articles.',
        [{ text: 'OK' }]
      );
      return;
    }
    navigation.navigate('ArticleForm', {});
  };

  const handleEditArticle = (articleId: string) => {
    navigation.navigate('ArticleForm', { articleId });
  };

  const handleDeleteArticle = (article: Article) => {
    Alert.alert(
      'Delete Article',
      `Are you sure you want to delete "${article.name}"? This action cannot be undone.`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => {
            try {
              await deleteArticle(article.id);
            } catch (err) {
              Alert.alert(
                'Error',
                err instanceof Error ? err.message : 'Failed to delete article'
              );
            }
          },
        },
      ]
    );
  };

  const getBusinessName = (businessId: string): string => {
    const business = businesses.find(b => b.id === businessId);
    return business?.name || 'Unknown Business';
  };

  const onRefresh = async () => {
    setRefreshing(true);
    // The data will automatically refresh through the reactive subscription
    setTimeout(() => setRefreshing(false), 1000);
  };

  const renderArticleItem = ({ item }: { item: Article }) => (
    <TouchableOpacity
      style={styles.articleItem}
      onPress={() => handleEditArticle(item.id)}
      activeOpacity={0.7}
    >
      <View style={styles.articleContent}>
        <Text style={styles.articleName}>{item.name}</Text>
        <Text style={styles.businessName}>{getBusinessName(item.business_id)}</Text>
        
        <View style={styles.articleDetails}>
          <View style={styles.detailItem}>
            <Text style={styles.detailLabel}>Quantity:</Text>
            <Text style={styles.detailValue}>{item.qty}</Text>
          </View>
          <View style={styles.detailItem}>
            <Text style={styles.detailLabel}>Price:</Text>
            <Text style={styles.detailValue}>{formatCurrency(item.selling_price)}</Text>
          </View>
        </View>
        
        <Text style={styles.articleDate}>
          Created: {formatDate(item.createdAt)}
        </Text>
      </View>
      
      <TouchableOpacity
        style={styles.deleteButton}
        onPress={() => handleDeleteArticle(item)}
        hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
      >
        <Text style={styles.deleteButtonText}>×</Text>
      </TouchableOpacity>
    </TouchableOpacity>
  );

  const renderHeader = () => (
    <View style={styles.header}>
      <View style={styles.statusContainer}>
        <View style={[styles.statusDot, { backgroundColor: isOnline ? COLORS.SUCCESS : COLORS.ERROR }]} />
        <Text style={styles.statusText}>
          {isOnline ? 'Online' : 'Offline'}
          {isSyncing && ' • Syncing...'}
        </Text>
      </View>
      
      <Button
        title="Add Article"
        onPress={handleCreateArticle}
        size="medium"
        style={styles.addButton}
      />
    </View>
  );

  if (loading && articles.length === 0) {
    return <LoadingSpinner message="Loading articles..." />;
  }

  if (error && articles.length === 0) {
    return (
      <View style={styles.container}>
        {renderHeader()}
        <EmptyState
          title="Error"
          message={error}
          actionTitle="Retry"
          onAction={onRefresh}
        />
      </View>
    );
  }

  if (articles.length === 0) {
    return (
      <View style={styles.container}>
        {renderHeader()}
        <EmptyState
          title="No Articles"
          message={businesses.length === 0 
            ? "You need to create a business first before adding articles."
            : "You haven't created any articles yet. Tap the button above to get started."
          }
          actionTitle={businesses.length === 0 ? undefined : "Create First Article"}
          onAction={businesses.length === 0 ? undefined : handleCreateArticle}
        />
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <FlatList
        data={articles}
        renderItem={renderArticleItem}
        keyExtractor={(item) => item.id}
        ListHeaderComponent={renderHeader}
        contentContainerStyle={styles.listContent}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            tintColor={COLORS.PRIMARY}
          />
        }
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.BACKGROUND,
  },
  header: {
    padding: SPACING.MD,
    backgroundColor: COLORS.SURFACE,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.BORDER,
  },
  statusContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: SPACING.SM,
  },
  statusDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginRight: SPACING.XS,
  },
  statusText: {
    fontSize: TYPOGRAPHY.FONT_SIZE.SM,
    color: COLORS.TEXT_SECONDARY,
  },
  addButton: {
    alignSelf: 'stretch',
  },
  listContent: {
    flexGrow: 1,
  },
  articleItem: {
    backgroundColor: COLORS.SURFACE,
    marginHorizontal: SPACING.MD,
    marginVertical: SPACING.XS,
    padding: SPACING.MD,
    borderRadius: 8,
    flexDirection: 'row',
    alignItems: 'flex-start',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  articleContent: {
    flex: 1,
  },
  articleName: {
    fontSize: TYPOGRAPHY.FONT_SIZE.LG,
    fontWeight: TYPOGRAPHY.FONT_WEIGHT.SEMIBOLD,
    color: COLORS.TEXT_PRIMARY,
    marginBottom: SPACING.XS,
  },
  businessName: {
    fontSize: TYPOGRAPHY.FONT_SIZE.SM,
    color: COLORS.PRIMARY,
    marginBottom: SPACING.SM,
  },
  articleDetails: {
    flexDirection: 'row',
    marginBottom: SPACING.SM,
  },
  detailItem: {
    flex: 1,
    flexDirection: 'row',
  },
  detailLabel: {
    fontSize: TYPOGRAPHY.FONT_SIZE.SM,
    color: COLORS.TEXT_SECONDARY,
    marginRight: SPACING.XS,
  },
  detailValue: {
    fontSize: TYPOGRAPHY.FONT_SIZE.SM,
    fontWeight: TYPOGRAPHY.FONT_WEIGHT.MEDIUM,
    color: COLORS.TEXT_PRIMARY,
  },
  articleDate: {
    fontSize: TYPOGRAPHY.FONT_SIZE.XS,
    color: COLORS.TEXT_SECONDARY,
  },
  deleteButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: COLORS.ERROR,
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: SPACING.SM,
  },
  deleteButtonText: {
    color: COLORS.SURFACE,
    fontSize: 20,
    fontWeight: TYPOGRAPHY.FONT_WEIGHT.BOLD,
    lineHeight: 20,
  },
});

export default ArticleListScreen;

