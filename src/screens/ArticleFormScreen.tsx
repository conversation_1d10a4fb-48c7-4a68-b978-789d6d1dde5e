import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  StyleSheet,
  Alert,
  KeyboardAvoidingView,
  Platform,
  TouchableOpacity,
} from 'react-native';
import { useNavigation, useRoute, RouteProp as NavigationRouteProp } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';

import { useArticles, useArticle, useBusinesses } from '../hooks/useDatabase';
import { ArticleInput } from '../types/Article';
import { Business } from '../types/Business';
import { RootStackParamList } from '../navigation/AppNavigator';

import Input from '../components/common/Input';
import Button from '../components/common/Button';
import LoadingSpinner from '../components/common/LoadingSpinner';
import { COLORS, SPACING, TYPOGRAPHY } from '../utils/constants';

type NavigationProp = StackNavigationProp<RootStackParamList, 'ArticleForm'>;
type ArticleFormRouteProp = NavigationRouteProp<RootStackParamList, 'ArticleForm'>;

interface BusinessPickerProps {
  businesses: Business[];
  selectedBusinessId: string;
  onSelect: (businessId: string) => void;
  error?: string;
}

const BusinessPicker: React.FC<BusinessPickerProps> = ({
  businesses,
  selectedBusinessId,
  onSelect,
  error,
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const selectedBusiness = businesses.find(b => b.id === selectedBusinessId);

  return (
    <View style={styles.pickerContainer}>
      <Text style={styles.pickerLabel}>
        Business <Text style={styles.required}>*</Text>
      </Text>
      
      <TouchableOpacity
        style={[styles.pickerButton, error && styles.pickerError]}
        onPress={() => setIsOpen(!isOpen)}
      >
        <Text style={[styles.pickerText, !selectedBusiness && styles.pickerPlaceholder]}>
          {selectedBusiness ? selectedBusiness.name : 'Select a business'}
        </Text>
        <Text style={styles.pickerArrow}>{isOpen ? '▲' : '▼'}</Text>
      </TouchableOpacity>
      
      {isOpen && (
        <View style={styles.pickerDropdown}>
          {businesses.map((business) => (
            <TouchableOpacity
              key={business.id}
              style={[
                styles.pickerOption,
                business.id === selectedBusinessId && styles.pickerOptionSelected,
              ]}
              onPress={() => {
                onSelect(business.id);
                setIsOpen(false);
              }}
            >
              <Text
                style={[
                  styles.pickerOptionText,
                  business.id === selectedBusinessId && styles.pickerOptionTextSelected,
                ]}
              >
                {business.name}
              </Text>
            </TouchableOpacity>
          ))}
        </View>
      )}
      
      {error && <Text style={styles.errorText}>{error}</Text>}
    </View>
  );
};

const ArticleFormScreen: React.FC = () => {
  const navigation = useNavigation<NavigationProp>();
  const route = useRoute<ArticleFormRouteProp>();
  const { articleId, businessId } = route.params;

  const { createArticle, updateArticle } = useArticles();
  const { article, loading: articleLoading } = useArticle(articleId || null);
  const { businesses, loading: businessesLoading } = useBusinesses();

  const [formData, setFormData] = useState<ArticleInput>({
    name: '',
    qty: 0,
    selling_price: 0,
    business_id: businessId || '',
  });
  const [errors, setErrors] = useState<{ [key: string]: string }>({});
  const [loading, setLoading] = useState(false);

  const isEditing = !!articleId;

  useEffect(() => {
    if (isEditing && article) {
      setFormData({
        name: article.name,
        qty: article.qty,
        selling_price: article.selling_price,
        business_id: article.business_id,
      });
    }
  }, [isEditing, article]);

  const validateForm = (): boolean => {
    const newErrors: { [key: string]: string } = {};

    if (!formData.name.trim()) {
      newErrors.name = 'Article name is required';
    } else if (formData.name.trim().length > 255) {
      newErrors.name = 'Article name must be less than 255 characters';
    }

    if (formData.qty < 0) {
      newErrors.qty = 'Quantity must be a positive number';
    }

    if (formData.selling_price < 0) {
      newErrors.selling_price = 'Selling price must be a positive number';
    }

    if (!formData.business_id) {
      newErrors.business_id = 'Please select a business';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async () => {
    if (!validateForm()) {
      return;
    }

    setLoading(true);
    setErrors({});

    try {
      if (isEditing && articleId) {
        await updateArticle(articleId, formData);
        Alert.alert('Success', 'Article updated successfully', [
          { text: 'OK', onPress: () => navigation.goBack() },
        ]);
      } else {
        await createArticle(formData);
        Alert.alert('Success', 'Article created successfully', [
          { text: 'OK', onPress: () => navigation.goBack() },
        ]);
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'An error occurred';
      Alert.alert('Error', errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    navigation.goBack();
  };

  if ((isEditing && articleLoading) || businessesLoading) {
    return <LoadingSpinner message="Loading..." />;
  }

  if (businesses.length === 0) {
    return (
      <View style={styles.container}>
        <View style={styles.emptyContainer}>
          <Text style={styles.emptyTitle}>No Businesses Available</Text>
          <Text style={styles.emptyMessage}>
            You need to create at least one business before adding articles.
          </Text>
          <Button
            title="Go Back"
            onPress={handleCancel}
            style={styles.emptyButton}
          />
        </View>
      </View>
    );
  }

  return (
    <KeyboardAvoidingView
      style={styles.container}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
    >
      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        keyboardShouldPersistTaps="handled"
      >
        <View style={styles.form}>
          <Input
            label="Article Name"
            value={formData.name}
            onChangeText={(text) => setFormData({ ...formData, name: text })}
            placeholder="Enter article name"
            error={errors.name}
            required
            autoFocus
            maxLength={255}
          />

          <BusinessPicker
            businesses={businesses}
            selectedBusinessId={formData.business_id}
            onSelect={(businessId) => setFormData({ ...formData, business_id: businessId })}
            error={errors.business_id}
          />

          <Input
            label="Quantity"
            value={formData.qty.toString()}
            onChangeText={(text) => {
              const qty = parseInt(text, 10) || 0;
              setFormData({ ...formData, qty });
            }}
            placeholder="Enter quantity"
            error={errors.qty}
            keyboardType="numeric"
            required
          />

          <Input
            label="Selling Price"
            value={formData.selling_price.toString()}
            onChangeText={(text) => {
              const price = parseFloat(text) || 0;
              setFormData({ ...formData, selling_price: price });
            }}
            placeholder="Enter selling price"
            error={errors.selling_price}
            keyboardType="decimal-pad"
            required
          />
        </View>

        <View style={styles.buttonContainer}>
          <Button
            title="Cancel"
            onPress={handleCancel}
            variant="secondary"
            style={styles.cancelButton}
          />
          <Button
            title={isEditing ? 'Update' : 'Create'}
            onPress={handleSubmit}
            loading={loading}
            style={styles.submitButton}
          />
        </View>
      </ScrollView>
    </KeyboardAvoidingView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.BACKGROUND,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
  },
  form: {
    flex: 1,
    padding: SPACING.MD,
  },
  buttonContainer: {
    flexDirection: 'row',
    padding: SPACING.MD,
    paddingTop: 0,
    gap: SPACING.SM,
  },
  cancelButton: {
    flex: 1,
  },
  submitButton: {
    flex: 1,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: SPACING.XL,
  },
  emptyTitle: {
    fontSize: TYPOGRAPHY.FONT_SIZE.XL,
    fontWeight: TYPOGRAPHY.FONT_WEIGHT.BOLD,
    color: COLORS.TEXT_PRIMARY,
    textAlign: 'center',
    marginBottom: SPACING.SM,
  },
  emptyMessage: {
    fontSize: TYPOGRAPHY.FONT_SIZE.MD,
    color: COLORS.TEXT_SECONDARY,
    textAlign: 'center',
    lineHeight: 22,
    marginBottom: SPACING.LG,
  },
  emptyButton: {
    minWidth: 120,
  },
  pickerContainer: {
    marginBottom: SPACING.MD,
  },
  pickerLabel: {
    fontSize: TYPOGRAPHY.FONT_SIZE.SM,
    fontWeight: TYPOGRAPHY.FONT_WEIGHT.MEDIUM,
    color: COLORS.TEXT_PRIMARY,
    marginBottom: SPACING.XS,
  },
  required: {
    color: COLORS.ERROR,
  },
  pickerButton: {
    borderWidth: 1,
    borderColor: COLORS.BORDER,
    borderRadius: 8,
    paddingHorizontal: SPACING.MD,
    paddingVertical: SPACING.SM,
    backgroundColor: COLORS.SURFACE,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    minHeight: 44,
  },
  pickerError: {
    borderColor: COLORS.ERROR,
  },
  pickerText: {
    fontSize: TYPOGRAPHY.FONT_SIZE.MD,
    color: COLORS.TEXT_PRIMARY,
    flex: 1,
  },
  pickerPlaceholder: {
    color: COLORS.TEXT_SECONDARY,
  },
  pickerArrow: {
    fontSize: TYPOGRAPHY.FONT_SIZE.SM,
    color: COLORS.TEXT_SECONDARY,
    marginLeft: SPACING.SM,
  },
  pickerDropdown: {
    borderWidth: 1,
    borderColor: COLORS.BORDER,
    borderTopWidth: 0,
    borderBottomLeftRadius: 8,
    borderBottomRightRadius: 8,
    backgroundColor: COLORS.SURFACE,
    maxHeight: 200,
  },
  pickerOption: {
    paddingHorizontal: SPACING.MD,
    paddingVertical: SPACING.SM,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.BORDER,
  },
  pickerOptionSelected: {
    backgroundColor: COLORS.PRIMARY + '10',
  },
  pickerOptionText: {
    fontSize: TYPOGRAPHY.FONT_SIZE.MD,
    color: COLORS.TEXT_PRIMARY,
  },
  pickerOptionTextSelected: {
    color: COLORS.PRIMARY,
    fontWeight: TYPOGRAPHY.FONT_WEIGHT.MEDIUM,
  },
  errorText: {
    fontSize: TYPOGRAPHY.FONT_SIZE.SM,
    color: COLORS.ERROR,
    marginTop: SPACING.XS,
  },
});

export default ArticleFormScreen;

