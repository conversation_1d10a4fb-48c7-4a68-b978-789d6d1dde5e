import { RxDatabase, RxCollection } from 'rxdb';
import { BusinessDocument } from './Business';
import { ArticleDocument } from './Article';

export interface DatabaseCollections {
  businesses: RxCollection<BusinessDocument>;
  articles: RxCollection<ArticleDocument>;
}

export type AppDatabase = RxDatabase<DatabaseCollections>;

export interface SyncStatus {
  isOnline: boolean;
  isSyncing: boolean;
  lastSyncTime?: Date;
  syncError?: string;
}

