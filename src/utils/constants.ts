// Database Configuration
export const DATABASE_NAME = 'offline_first_crud_db';
export const DATABASE_VERSION = 1;

// CouchDB Configuration
export const COUCHDB_URL = 'https://your-couchdb-instance.com'; // Replace with actual CouchDB URL
export const COUCHDB_USERNAME = 'your-username'; // Replace with actual username
export const COUCHDB_PASSWORD = 'your-password'; // Replace with actual password

// Collection Names
export const COLLECTIONS = {
  BUSINESSES: 'businesses',
  ARTICLES: 'articles',
} as const;

// Sync Configuration
export const SYNC_CONFIG = {
  RETRY_DELAY: 5000, // 5 seconds
  MAX_RETRIES: 3,
  BATCH_SIZE: 100,
} as const;

// UI Constants
export const COLORS = {
  PRIMARY: '#007AFF',
  SECONDARY: '#5856D6',
  SUCCESS: '#34C759',
  WARNING: '#FF9500',
  ERROR: '#FF3B30',
  BACKGROUND: '#F2F2F7',
  SURFACE: '#FFFFFF',
  TEXT_PRIMARY: '#000000',
  TEXT_SECONDARY: '#8E8E93',
  BORDER: '#C6C6C8',
} as const;

export const SPACING = {
  XS: 4,
  SM: 8,
  MD: 16,
  LG: 24,
  XL: 32,
} as const;

export const TYPOGRAPHY = {
  FONT_SIZE: {
    XS: 12,
    SM: 14,
    MD: 16,
    LG: 18,
    XL: 24,
    XXL: 32,
  },
  FONT_WEIGHT: {
    REGULAR: '400' as const,
    MEDIUM: '500' as const,
    SEMIBOLD: '600' as const,
    BOLD: '700' as const,
  },
} as const;

