import { BusinessInput } from '../types/Business';
import { ArticleInput } from '../types/Article';

export interface ValidationError {
  field: string;
  message: string;
}

export const validateBusiness = (business: BusinessInput): ValidationError[] => {
  const errors: ValidationError[] = [];

  if (!business.name || business.name.trim().length === 0) {
    errors.push({
      field: 'name',
      message: 'Business name is required',
    });
  }

  if (business.name && business.name.trim().length > 255) {
    errors.push({
      field: 'name',
      message: 'Business name must be less than 255 characters',
    });
  }

  return errors;
};

export const validateArticle = (article: ArticleInput): ValidationError[] => {
  const errors: ValidationError[] = [];

  if (!article.name || article.name.trim().length === 0) {
    errors.push({
      field: 'name',
      message: 'Article name is required',
    });
  }

  if (article.name && article.name.trim().length > 255) {
    errors.push({
      field: 'name',
      message: 'Article name must be less than 255 characters',
    });
  }

  if (article.qty < 0) {
    errors.push({
      field: 'qty',
      message: 'Quantity must be a positive number',
    });
  }

  if (article.selling_price < 0) {
    errors.push({
      field: 'selling_price',
      message: 'Selling price must be a positive number',
    });
  }

  if (!article.business_id || article.business_id.trim().length === 0) {
    errors.push({
      field: 'business_id',
      message: 'Business selection is required',
    });
  }

  return errors;
};

export const formatValidationErrors = (errors: ValidationError[]): string => {
  return errors.map(error => error.message).join('\n');
};

