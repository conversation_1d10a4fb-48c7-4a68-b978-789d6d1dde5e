{"name": "OfflineFirstCRUD", "version": "0.0.1", "private": true, "scripts": {"android": "react-native run-android", "ios": "react-native run-ios", "lint": "eslint .", "start": "react-native start", "test": "jest", "go": "npm run android && npm start"}, "dependencies": {"@react-native-community/netinfo": "^11.4.1", "@react-native/new-app-screen": "0.81.4", "@react-navigation/bottom-tabs": "^7.4.7", "@react-navigation/native": "^7.1.17", "@react-navigation/stack": "^7.4.8", "@types/uuid": "^10.0.0", "pouchdb-adapter-http": "^9.0.0", "pouchdb-adapter-idb": "^9.0.0", "pouchdb-adapter-react-native-sqlite": "^4.1.2", "pouchdb-replication": "^9.0.0", "react": "19.1.0", "react-native": "0.81.4", "react-native-gesture-handler": "^2.28.0", "react-native-reanimated": "^4.1.0", "react-native-safe-area-context": "^5.5.2", "react-native-screens": "^4.16.0", "react-native-sqlite-storage": "^6.0.1", "react-native-uuid": "^2.0.3", "react-native-vector-icons": "^10.3.0", "react-native-worklets": "^0.5.1", "rxdb": "^16.19.0", "rxjs": "^7.8.2"}, "devDependencies": {"@babel/core": "^7.25.2", "@babel/preset-env": "^7.25.3", "@babel/runtime": "^7.25.0", "@react-native-community/cli": "20.0.0", "@react-native-community/cli-platform-android": "20.0.0", "@react-native-community/cli-platform-ios": "20.0.0", "@react-native/babel-preset": "0.81.4", "@react-native/eslint-config": "0.81.4", "@react-native/metro-config": "0.81.4", "@react-native/typescript-config": "0.81.4", "@types/jest": "^29.5.13", "@types/react": "^19.1.0", "@types/react-test-renderer": "^19.1.0", "eslint": "^8.19.0", "jest": "^29.6.3", "prettier": "2.8.8", "react-test-renderer": "19.1.0", "typescript": "^5.8.3"}, "engines": {"node": ">=20"}}